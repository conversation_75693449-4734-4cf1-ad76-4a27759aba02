"""
三层架构系统整合

实现客户端层、云端层、知识蒸馏层的完整协同工作流程：
1. 客户端处理用户交互，生成用户表示
2. 云端LLM进行推荐优化，生成Top-K推荐
3. 知识蒸馏层提取知识，更新客户端模型
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
import time

# 设置日志
logger = logging.getLogger(__name__)

# 导入三层组件
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from models.client.cf_srec_client import CFSRecClient, ClientCommunicator
    from models.cloud.llm_cloud_server import LLMCloudServer, CloudCommunicator
    from models.knowledge_distillation.distillation_engine import DistillationEngine
except ImportError as e:
    print(f"Import error: {e}, using mock classes for demonstration")
    # 创建模拟类用于演示
    class CFSRecClient:
        def __init__(self, config):
            self.config = config
            self.hidden_units = config.get('hidden_units', 64)
        def generate_user_representation(self, interactions):
            return torch.randn(self.hidden_units)
        def update_from_cloud_feedback(self, feedback):
            pass
        def parameters(self):
            return [torch.randn(100, 64)]

    class ClientCommunicator:
        def __init__(self, config): pass

    class LLMCloudServer:
        def __init__(self, config):
            self.llm_hidden_size = 2048
        def generate_top_k_recommendations(self, user_mu, user_id, top_k=10):
            return {
                'recommendations': list(range(1, top_k+1)),
                'scores': [0.9-i*0.05 for i in range(top_k)],
                'explanations': [f"推荐{i}" for i in range(top_k)]
            }

    class CloudCommunicator:
        def __init__(self, config): pass
        def send_to_knowledge_distillation(self, data):
            return {'teacher_knowledge': {}}

    class MockTeacherGenerator:
        def extract_teacher_knowledge(self, cloud_performance):
            return {'feature_knowledge': torch.randn(4, 64), 'teacher_confidence': torch.tensor([0.8])}

    class MockUpdateDistributor:
        def generate_model_updates(self, student_model, teacher_knowledge):
            return {'version': 1, 'updates': {}, 'metadata': {'quality_score': 0.8}}
        def distribute_updates(self, model_updates, client_list):
            return {'distribution_summary': {'success_rate': 0.95}}

    class DistillationEngine:
        def __init__(self, config):
            self.teacher_generator = MockTeacherGenerator()
            self.update_distributor = MockUpdateDistributor()
        def run_distillation_cycle(self, cloud_outputs, student_model, optimizer, client_list):
            return {
                'teacher_knowledge_quality': {'confidence_score': 0.85},
                'optimization_metrics': {'total_loss': 0.1, 'grad_norm': 0.5},
                'model_update_info': {'quality_score': 0.8},
                'distribution_result': {'success_rate': 0.95},
                'convergence_status': {'converged': False}
            }

logger = logging.getLogger(__name__)


class ThreeLayerRecommendationSystem:
    """
    三层推荐系统
    
    整合客户端层、云端层、知识蒸馏层的完整工作流程
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # 初始化三层组件
        self._init_client_layer()
        self._init_cloud_layer()
        self._init_knowledge_distillation_layer()
        
        # 系统状态
        self.system_metrics = {
            'total_requests': 0,
            'successful_recommendations': 0,
            'knowledge_updates': 0,
            'average_response_time': 0.0
        }
        
        logger.info("ThreeLayerRecommendationSystem initialized")
    
    def _init_client_layer(self):
        """初始化客户端层"""
        client_config = self.config.get('client', {})
        client_config.update({
            'item_num': self.config.get('item_num', 10000),
            'hidden_units': self.config.get('client_hidden_units', 64),
            'device': 'cpu'  # 客户端通常使用CPU
        })
        
        self.client_model = CFSRecClient(client_config)
        self.client_communicator = ClientCommunicator(client_config)
        
        logger.info("Client layer initialized")
    
    def _init_cloud_layer(self):
        """初始化云端层"""
        cloud_config = self.config.get('cloud', {})
        cloud_config.update({
            'item_num': self.config.get('item_num', 10000),
            'user_repr_dim': self.config.get('client_hidden_units', 64),
            'device': self.config.get('cloud_device', 'cuda:0')
        })
        
        self.cloud_server = LLMCloudServer(cloud_config)
        self.cloud_communicator = CloudCommunicator(cloud_config)
        
        logger.info("Cloud layer initialized")
    
    def _init_knowledge_distillation_layer(self):
        """初始化知识蒸馏层"""
        kd_config = self.config.get('knowledge_distillation', {})
        kd_config.update({
            'llm_hidden_size': self.cloud_server.llm_hidden_size,
            'client_hidden_size': self.config.get('client_hidden_units', 64),
            'device': self.config.get('cloud_device', 'cuda:0')
        })
        
        self.distillation_engine = DistillationEngine(kd_config)
        
        # 创建学生模型优化器（用于知识蒸馏）
        self.student_optimizer = torch.optim.Adam(
            self.client_model.parameters(), 
            lr=kd_config.get('learning_rate', 1e-4)
        )
        
        logger.info("Knowledge distillation layer initialized")
    
    def process_user_request(self, user_id: str, user_interactions: List[int]) -> Dict[str, Any]:
        """
        处理用户推荐请求的完整流程
        
        Args:
            user_id: 用户ID
            user_interactions: 用户交互历史
            
        Returns:
            recommendation_result: 推荐结果
        """
        start_time = time.time()
        
        try:
            # 1. 客户端层：生成用户表示
            logger.info(f"Step 1: Client processing for user {user_id}")
            user_mu = self.client_model.generate_user_representation(user_interactions)
            
            # 2. 客户端向云端发送用户表示
            logger.info(f"Step 2: Sending user representation to cloud")
            cloud_request = {
                'user_id': user_id,
                'user_representation': user_mu.tolist(),
                'timestamp': time.time()
            }
            
            # 3. 云端层：推荐优化和Top-K生成
            logger.info(f"Step 3: Cloud processing and recommendation generation")
            cloud_recommendation = self.cloud_server.generate_top_k_recommendations(
                user_mu, user_id, top_k=self.config.get('top_k', 10)
            )
            
            # 4. 云端向知识蒸馏层发送数据
            logger.info(f"Step 4: Sending data to knowledge distillation layer")
            kd_request = self.cloud_communicator.send_to_knowledge_distillation(cloud_recommendation)
            
            # 5. 知识蒸馏层：提取知识并生成更新
            logger.info(f"Step 5: Knowledge distillation and model update")
            cloud_outputs = {
                'optimized_repr': torch.randn(1, self.cloud_server.llm_hidden_size),  # 模拟
                'recommendation_scores': torch.tensor([cloud_recommendation['scores']])
            }
            
            # 运行知识蒸馏周期
            distillation_results = self.distillation_engine.run_distillation_cycle(
                cloud_outputs, 
                self.client_model, 
                self.student_optimizer,
                [user_id]
            )
            
            # 6. 客户端接收推荐列表
            logger.info(f"Step 6: Client receiving recommendations")
            final_recommendations = {
                'user_id': user_id,
                'recommendations': cloud_recommendation['recommendations'],
                'scores': cloud_recommendation['scores'],
                'explanations': cloud_recommendation['explanations'],
                'system_metrics': {
                    'response_time': time.time() - start_time,
                    'knowledge_quality': distillation_results['teacher_knowledge_quality'],
                    'distillation_loss': distillation_results['optimization_metrics']['total_loss']
                }
            }
            
            # 更新系统指标
            self._update_system_metrics(final_recommendations)
            
            return final_recommendations
            
        except Exception as e:
            logger.error(f"Error processing user request: {e}")
            return {
                'user_id': user_id,
                'error': str(e),
                'recommendations': [],
                'scores': [],
                'system_metrics': {'response_time': time.time() - start_time}
            }
    
    def run_knowledge_update_cycle(self, client_list: List[str]) -> Dict[str, Any]:
        """
        运行知识更新周期
        
        Args:
            client_list: 客户端列表
            
        Returns:
            update_results: 更新结果
        """
        logger.info("Running knowledge update cycle")
        
        # 1. 收集云端性能数据
        cloud_performance = self._collect_cloud_performance()
        
        # 2. 生成教师知识
        teacher_knowledge = self.distillation_engine.teacher_generator.extract_teacher_knowledge(
            cloud_performance
        )
        
        # 3. 生成模型更新
        model_updates = self.distillation_engine.update_distributor.generate_model_updates(
            self.client_model, teacher_knowledge
        )
        
        # 4. 分发更新到客户端
        distribution_result = self.distillation_engine.update_distributor.distribute_updates(
            model_updates, client_list
        )
        
        # 5. 应用更新到本地客户端模型
        self.client_model.update_from_cloud_feedback({
            'model_updates': model_updates['updates']
        })
        
        self.system_metrics['knowledge_updates'] += 1
        
        return {
            'update_version': model_updates['version'],
            'distribution_success_rate': distribution_result['distribution_summary']['success_rate'],
            'update_quality': model_updates['metadata']['quality_score'],
            'affected_clients': len(client_list)
        }
    
    def _collect_cloud_performance(self) -> Dict[str, torch.Tensor]:
        """收集云端性能数据"""
        # 模拟云端性能数据
        return {
            'optimized_repr': torch.randn(4, self.cloud_server.llm_hidden_size),
            'recommendation_scores': torch.randn(4, self.config.get('item_num', 10000))
        }
    
    def _update_system_metrics(self, recommendation_result: Dict[str, Any]):
        """更新系统指标"""
        self.system_metrics['total_requests'] += 1
        
        if 'error' not in recommendation_result:
            self.system_metrics['successful_recommendations'] += 1
        
        # 更新平均响应时间
        response_time = recommendation_result['system_metrics']['response_time']
        total_requests = self.system_metrics['total_requests']
        current_avg = self.system_metrics['average_response_time']
        
        self.system_metrics['average_response_time'] = (
            (current_avg * (total_requests - 1) + response_time) / total_requests
        )
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        success_rate = (
            self.system_metrics['successful_recommendations'] / 
            max(self.system_metrics['total_requests'], 1)
        )
        
        return {
            'system_metrics': self.system_metrics,
            'success_rate': success_rate,
            'client_model_params': sum(p.numel() for p in self.client_model.parameters()),
            'cloud_model_status': 'active',
            'distillation_engine_status': 'active'
        }


def demo_three_layer_system():
    """演示三层系统完整工作流程"""
    print("🏗️  三层架构系统演示")
    print("=" * 60)
    
    # 系统配置
    config = {
        'item_num': 1000,
        'client_hidden_units': 64,
        'top_k': 10,
        'cloud_device': 'cuda:0' if torch.cuda.is_available() else 'cpu',
        'client': {
            'num_blocks': 2,
            'num_heads': 1,
            'dropout_rate': 0.2,
            'max_sequence_length': 50
        },
        'cloud': {
            'llm_model': 'llama-3b',
            'load_in_8bit': True
        },
        'knowledge_distillation': {
            'distillation_temperature': 4.0,
            'learning_rate': 1e-4,
            'distillation_alpha': 0.7
        }
    }
    
    # 初始化三层系统
    print("🚀 初始化三层推荐系统...")
    system = ThreeLayerRecommendationSystem(config)
    
    print("✅ 系统初始化完成")
    print(f"📊 系统状态: {system.get_system_status()}")
    
    # 模拟用户请求
    print(f"\n👤 处理用户推荐请求...")
    
    # 用户1: 动作电影爱好者
    user1_interactions = [120, 135, 142, 156, 167, 178, 189, 195]
    result1 = system.process_user_request("user_001", user1_interactions)
    
    print(f"\n🎬 用户1推荐结果:")
    print(f"   - 用户ID: {result1['user_id']}")
    print(f"   - Top-5推荐: {result1['recommendations'][:5]}")
    print(f"   - 推荐分数: {[f'{score:.3f}' for score in result1['scores'][:5]]}")
    print(f"   - 响应时间: {result1['system_metrics']['response_time']:.3f}秒")
    
    # 用户2: 喜剧电影爱好者
    user2_interactions = [305, 318, 325, 334, 347, 356, 362, 371]
    result2 = system.process_user_request("user_002", user2_interactions)
    
    print(f"\n🎭 用户2推荐结果:")
    print(f"   - 用户ID: {result2['user_id']}")
    print(f"   - Top-5推荐: {result2['recommendations'][:5]}")
    print(f"   - 推荐分数: {[f'{score:.3f}' for score in result2['scores'][:5]]}")
    print(f"   - 响应时间: {result2['system_metrics']['response_time']:.3f}秒")
    
    # 运行知识更新周期
    print(f"\n🧠 运行知识更新周期...")
    client_list = ["user_001", "user_002", "user_003", "user_004", "user_005"]
    update_results = system.run_knowledge_update_cycle(client_list)
    
    print(f"📈 知识更新结果:")
    print(f"   - 更新版本: {update_results['update_version']}")
    print(f"   - 分发成功率: {update_results['distribution_success_rate']:.1%}")
    print(f"   - 更新质量: {update_results['update_quality']:.3f}")
    print(f"   - 影响客户端: {update_results['affected_clients']}个")
    
    # 最终系统状态
    final_status = system.get_system_status()
    print(f"\n📊 最终系统状态:")
    print(f"   - 总请求数: {final_status['system_metrics']['total_requests']}")
    print(f"   - 成功率: {final_status['success_rate']:.1%}")
    print(f"   - 平均响应时间: {final_status['system_metrics']['average_response_time']:.3f}秒")
    print(f"   - 知识更新次数: {final_status['system_metrics']['knowledge_updates']}")
    
    print(f"\n💡 三层架构特点:")
    print(f"   📱 客户端层: 轻量级CF-SRec，本地用户表示生成")
    print(f"   ☁️  云端层: 强大LLM推荐优化，高质量推荐生成")
    print(f"   🧠 知识蒸馏层: 智能知识提取，持续模型改进")
    print(f"   🔄 协同工作: 三层无缝协作，端到端推荐服务")


def run_system_benchmark(num_users: int = 100, num_requests_per_user: int = 5):
    """运行系统基准测试"""
    print(f"\n🏃 运行系统基准测试")
    print(f"📊 测试规模: {num_users}用户 × {num_requests_per_user}请求 = {num_users * num_requests_per_user}总请求")
    
    # 简化配置用于基准测试
    config = {
        'item_num': 1000,
        'client_hidden_units': 32,  # 减小模型以加快测试
        'top_k': 5,
        'cloud_device': 'cpu',  # 使用CPU以确保兼容性
        'client': {'num_blocks': 1, 'num_heads': 1, 'max_sequence_length': 20},
        'cloud': {'llm_model': 'mock', 'load_in_8bit': False},
        'knowledge_distillation': {'distillation_temperature': 2.0}
    }
    
    system = ThreeLayerRecommendationSystem(config)
    
    start_time = time.time()
    successful_requests = 0
    
    for user_id in range(num_users):
        for request_id in range(num_requests_per_user):
            # 生成随机用户交互
            user_interactions = np.random.randint(1, 1000, size=10).tolist()
            
            result = system.process_user_request(f"user_{user_id:03d}", user_interactions)
            
            if 'error' not in result:
                successful_requests += 1
    
    total_time = time.time() - start_time
    total_requests = num_users * num_requests_per_user
    
    print(f"\n📈 基准测试结果:")
    print(f"   - 总耗时: {total_time:.2f}秒")
    print(f"   - 平均每请求: {total_time/total_requests:.3f}秒")
    print(f"   - 吞吐量: {total_requests/total_time:.1f}请求/秒")
    print(f"   - 成功率: {successful_requests/total_requests:.1%}")


if __name__ == '__main__':
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行演示
    demo_three_layer_system()
    
    # 运行基准测试（可选）
    # run_system_benchmark(num_users=10, num_requests_per_user=2)
