"""
LLM-SRec纯正大小模型协同训练器

实现大小模型协同推荐系统的训练流程：
1. 小模型预训练
2. 大模型初始化
3. 协同联合训练
4. 知识蒸馏优化
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
import logging
import yaml
from typing import Dict, List, Tuple, Optional, Any
from tqdm import tqdm
import wandb
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.collaborative.collaborative_model import CollaborativeRecommendationModel
from utils.data_utils import RecommendationDataLoader
from utils.evaluation import RecommendationEvaluator
from utils.model_utils import save_checkpoint, load_checkpoint, setup_logging

logger = logging.getLogger(__name__)


class CollaborativeTrainer:
    """
    大小模型协同训练器
    
    实现纯正的大小模型协同推荐系统训练：
    1. 端到端联合训练
    2. 知识蒸馏优化
    3. 特征对齐学习
    4. 多目标优化
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.device = config.get('device', 'cuda:0')
        
        # 训练配置
        self.num_epochs = config['training']['num_epochs']
        self.learning_rate = config['training']['learning_rate']
        self.batch_size = config['training']['batch_size']
        self.warmup_steps = config['training'].get('warmup_steps', 1000)
        self.gradient_clip = config['training'].get('gradient_clip', 1.0)
        
        # 初始化组件
        self._init_logging()
        self._init_model()
        self._init_data()
        self._init_optimizer()
        self._init_evaluator()
        
        # 训练状态
        self.current_epoch = 0
        self.global_step = 0
        self.best_performance = 0.0
        self.patience_counter = 0
        
        logger.info("CollaborativeTrainer initialized successfully")
    
    def _init_logging(self):
        """初始化日志"""
        setup_logging(self.config.get('log_level', 'INFO'))
        
        # TensorBoard
        self.writer = SummaryWriter(
            log_dir=os.path.join(self.config['training']['output_dir'], 'tensorboard')
        )
        
        # Wandb（可选）
        if self.config['training'].get('use_wandb', False):
            wandb.init(
                project=self.config['training'].get('wandb_project', 'llm-srec-pure'),
                config=self.config,
                name=self.config['training'].get('experiment_name', 'collaborative_training')
            )
    
    def _init_model(self):
        """初始化模型"""
        self.model = CollaborativeRecommendationModel(self.config)
        self.model.to(self.device)
        
        # 模型参数统计
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        logger.info(f"Model initialized with {total_params:,} total parameters")
        logger.info(f"Trainable parameters: {trainable_params:,}")
    
    def _init_data(self):
        """初始化数据加载器"""
        data_config = self.config['data']
        
        self.data_loader = RecommendationDataLoader(data_config)
        
        # 获取数据加载器
        self.train_loader = self.data_loader.get_train_loader(
            batch_size=self.batch_size,
            shuffle=True
        )
        
        self.val_loader = self.data_loader.get_val_loader(
            batch_size=self.batch_size,
            shuffle=False
        )
        
        self.test_loader = self.data_loader.get_test_loader(
            batch_size=self.batch_size,
            shuffle=False
        )
        
        logger.info(f"Data loaders initialized")
        logger.info(f"Train batches: {len(self.train_loader)}")
        logger.info(f"Val batches: {len(self.val_loader)}")
        logger.info(f"Test batches: {len(self.test_loader)}")
    
    def _init_optimizer(self):
        """初始化优化器"""
        # 分层学习率
        small_model_params = []
        large_model_params = []
        collaboration_params = []
        
        for name, param in self.model.named_parameters():
            if 'item_emb' in name or 'pos_emb' in name or 'attention_layers' in name or 'feed_forward_layers' in name:
                small_model_params.append(param)
            elif 'recommendation_optimizer' in name or 'large_output' in name:
                large_model_params.append(param)
            else:
                collaboration_params.append(param)
        
        # 优化器参数组
        param_groups = [
            {'params': small_model_params, 'lr': self.learning_rate},
            {'params': large_model_params, 'lr': self.learning_rate * 0.1},  # 大模型用较小学习率
            {'params': collaboration_params, 'lr': self.learning_rate * 0.5}  # 协同组件用中等学习率
        ]
        
        self.optimizer = torch.optim.AdamW(
            param_groups,
            weight_decay=self.config['training'].get('weight_decay', 1e-5)
        )
        
        # 学习率调度器
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=self.num_epochs * len(self.train_loader),
            eta_min=self.learning_rate * 0.01
        )
        
        logger.info("Optimizer and scheduler initialized")
    
    def _init_evaluator(self):
        """初始化评估器"""
        self.evaluator = RecommendationEvaluator(
            metrics=['ndcg@10', 'hit_rate@10', 'recall@10', 'precision@10']
        )
    
    def train_one_epoch(self) -> Dict[str, float]:
        """
        训练一个epoch
        
        Returns:
            metrics: 训练指标
        """
        self.model.train()
        
        total_loss = 0.0
        total_small_loss = 0.0
        total_large_loss = 0.0
        total_collaborative_loss = 0.0
        total_distillation_loss = 0.0
        total_alignment_loss = 0.0
        
        progress_bar = tqdm(self.train_loader, desc=f"Epoch {self.current_epoch + 1}")
        
        for batch_idx, batch in enumerate(progress_bar):
            # 数据移动到设备
            input_ids = batch['input_ids'].to(self.device)
            attention_mask = batch['attention_mask'].to(self.device)
            targets = batch['targets'].to(self.device)
            
            # 前向传播
            outputs = self.model(input_ids, attention_mask, targets)
            
            # 损失
            loss = outputs['total_loss']
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            if self.gradient_clip > 0:
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.gradient_clip)
            
            self.optimizer.step()
            self.scheduler.step()
            
            # 累积损失
            total_loss += loss.item()
            total_small_loss += outputs['small_rec_loss'].item()
            total_large_loss += outputs['large_rec_loss'].item()
            total_collaborative_loss += outputs['collaborative_rec_loss'].item()
            total_distillation_loss += outputs['distillation_loss'].item()
            total_alignment_loss += outputs['alignment_loss'].item()
            
            self.global_step += 1
            
            # 更新进度条
            progress_bar.set_postfix({
                'Loss': f"{loss.item():.4f}",
                'LR': f"{self.scheduler.get_last_lr()[0]:.6f}"
            })
            
            # 记录到TensorBoard
            if self.global_step % 100 == 0:
                self.writer.add_scalar('Train/Loss', loss.item(), self.global_step)
                self.writer.add_scalar('Train/SmallLoss', outputs['small_rec_loss'].item(), self.global_step)
                self.writer.add_scalar('Train/LargeLoss', outputs['large_rec_loss'].item(), self.global_step)
                self.writer.add_scalar('Train/CollaborativeLoss', outputs['collaborative_rec_loss'].item(), self.global_step)
                self.writer.add_scalar('Train/DistillationLoss', outputs['distillation_loss'].item(), self.global_step)
                self.writer.add_scalar('Train/AlignmentLoss', outputs['alignment_loss'].item(), self.global_step)
                self.writer.add_scalar('Train/LearningRate', self.scheduler.get_last_lr()[0], self.global_step)
        
        # 计算平均损失
        num_batches = len(self.train_loader)
        metrics = {
            'train_loss': total_loss / num_batches,
            'train_small_loss': total_small_loss / num_batches,
            'train_large_loss': total_large_loss / num_batches,
            'train_collaborative_loss': total_collaborative_loss / num_batches,
            'train_distillation_loss': total_distillation_loss / num_batches,
            'train_alignment_loss': total_alignment_loss / num_batches
        }
        
        return metrics
    
    def evaluate(self, data_loader: DataLoader, split: str = 'val') -> Dict[str, float]:
        """
        评估模型
        
        Args:
            data_loader: 数据加载器
            split: 数据集分割名称
            
        Returns:
            metrics: 评估指标
        """
        self.model.eval()
        
        all_predictions = []
        all_targets = []
        total_loss = 0.0
        
        with torch.no_grad():
            for batch in tqdm(data_loader, desc=f"Evaluating {split}"):
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                targets = batch['targets'].to(self.device)
                
                # 前向传播
                outputs = self.model(input_ids, attention_mask, targets)
                
                # 累积损失
                total_loss += outputs['total_loss'].item()
                
                # 生成推荐
                recommendations = self.model.generate_recommendations(
                    input_ids, attention_mask, top_k=10
                )
                
                all_predictions.extend(recommendations.cpu().numpy())
                all_targets.extend(targets.cpu().numpy())
        
        # 计算评估指标
        eval_metrics = self.evaluator.evaluate(all_predictions, all_targets)
        eval_metrics[f'{split}_loss'] = total_loss / len(data_loader)
        
        return eval_metrics
    
    def train(self):
        """
        完整训练流程
        """
        logger.info("Starting collaborative training...")
        
        training_history = []
        
        for epoch in range(self.num_epochs):
            self.current_epoch = epoch
            
            # 训练一个epoch
            train_metrics = self.train_one_epoch()
            
            # 验证
            val_metrics = self.evaluate(self.val_loader, 'val')
            
            # 合并指标
            epoch_metrics = {**train_metrics, **val_metrics}
            training_history.append(epoch_metrics)
            
            # 记录指标
            self._log_metrics(epoch_metrics, epoch)
            
            # 早停检查
            current_performance = val_metrics.get('ndcg@10', 0.0)
            if current_performance > self.best_performance:
                self.best_performance = current_performance
                self.patience_counter = 0
                
                # 保存最佳模型
                self._save_best_model(epoch_metrics)
            else:
                self.patience_counter += 1
            
            # 早停
            patience = self.config['training'].get('patience', 10)
            if self.patience_counter >= patience:
                logger.info(f"Early stopping triggered after {patience} epochs without improvement")
                break
        
        # 最终测试
        logger.info("Training completed. Running final evaluation...")
        self._load_best_model()
        test_metrics = self.evaluate(self.test_loader, 'test')
        
        logger.info("Final Test Results:")
        for metric, value in test_metrics.items():
            logger.info(f"{metric}: {value:.4f}")
        
        # 清理
        self.writer.close()
        if self.config['training'].get('use_wandb', False):
            wandb.finish()
        
        return training_history, test_metrics
    
    def _log_metrics(self, metrics: Dict[str, float], epoch: int):
        """记录指标"""
        # 控制台输出
        logger.info(f"Epoch {epoch + 1}/{self.num_epochs}")
        logger.info(f"Train Loss: {metrics['train_loss']:.4f}")
        logger.info(f"Val Loss: {metrics['val_loss']:.4f}")
        logger.info(f"Val NDCG@10: {metrics.get('ndcg@10', 0.0):.4f}")
        logger.info(f"Val Hit Rate@10: {metrics.get('hit_rate@10', 0.0):.4f}")
        
        # TensorBoard
        for metric, value in metrics.items():
            self.writer.add_scalar(f'Epoch/{metric}', value, epoch)
        
        # Wandb
        if self.config['training'].get('use_wandb', False):
            wandb.log(metrics, step=epoch)
    
    def _save_best_model(self, metrics: Dict[str, float]):
        """保存最佳模型"""
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_performance': self.best_performance,
            'metrics': metrics,
            'config': self.config
        }
        
        save_path = os.path.join(self.config['training']['output_dir'], 'best_model.pt')
        save_checkpoint(checkpoint, save_path)
        
        logger.info(f"Best model saved with NDCG@10: {self.best_performance:.4f}")
    
    def _load_best_model(self):
        """加载最佳模型"""
        save_path = os.path.join(self.config['training']['output_dir'], 'best_model.pt')
        if os.path.exists(save_path):
            checkpoint = load_checkpoint(save_path)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            logger.info("Best model loaded for final evaluation")
        else:
            logger.warning("Best model not found, using current model")


def main():
    """主训练函数"""
    # 加载配置
    config_path = "config/collaborative_config.yaml"
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # 创建输出目录
    os.makedirs(config['training']['output_dir'], exist_ok=True)
    
    # 初始化训练器
    trainer = CollaborativeTrainer(config)
    
    # 开始训练
    training_history, test_metrics = trainer.train()
    
    print("Training completed successfully!")
    print(f"Best Test NDCG@10: {test_metrics.get('ndcg@10', 0.0):.4f}")


if __name__ == '__main__':
    main()
