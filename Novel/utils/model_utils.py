"""
模型工具函数

提供模型相关的辅助功能：
1. 模型保存和加载
2. 日志设置
3. 随机种子设置
4. 设备管理
"""

import os
import json
import logging
import torch
import numpy as np
import random
from typing import Dict, Any, Optional
from datetime import datetime


def setup_logging(log_level: str = 'INFO', log_file: Optional[str] = None):
    """
    设置日志配置
    
    Args:
        log_level: 日志级别
        log_file: 日志文件路径（可选）
    """
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 基础配置
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[]
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(log_format))
    
    # 获取根日志器
    root_logger = logging.getLogger()
    root_logger.handlers = []  # 清除现有处理器
    root_logger.addHandler(console_handler)
    
    # 文件处理器（如果指定了日志文件）
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(logging.Formatter(log_format))
        root_logger.addHandler(file_handler)
    
    # 设置第三方库日志级别
    logging.getLogger('transformers').setLevel(logging.WARNING)
    logging.getLogger('torch').setLevel(logging.WARNING)
    logging.getLogger('numpy').setLevel(logging.WARNING)


def set_random_seed(seed: int):
    """
    设置随机种子
    
    Args:
        seed: 随机种子
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def save_checkpoint(checkpoint: Dict[str, Any], filepath: str):
    """
    保存检查点
    
    Args:
        checkpoint: 检查点数据
        filepath: 保存路径
    """
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    torch.save(checkpoint, filepath)
    logging.info(f"Checkpoint saved to {filepath}")


def load_checkpoint(filepath: str) -> Dict[str, Any]:
    """
    加载检查点
    
    Args:
        filepath: 检查点文件路径
        
    Returns:
        checkpoint: 检查点数据
    """
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"Checkpoint file not found: {filepath}")
    
    checkpoint = torch.load(filepath, map_location='cpu')
    logging.info(f"Checkpoint loaded from {filepath}")
    return checkpoint


def save_config(config: Dict[str, Any], filepath: str):
    """
    保存配置文件
    
    Args:
        config: 配置字典
        filepath: 保存路径
    """
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    
    # 添加时间戳
    config['saved_at'] = datetime.now().isoformat()
    
    with open(filepath, 'w') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    logging.info(f"Config saved to {filepath}")


def load_config(filepath: str) -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        filepath: 配置文件路径
        
    Returns:
        config: 配置字典
    """
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"Config file not found: {filepath}")
    
    with open(filepath, 'r') as f:
        config = json.load(f)
    
    logging.info(f"Config loaded from {filepath}")
    return config


def get_device(device_str: str = 'auto') -> torch.device:
    """
    获取计算设备
    
    Args:
        device_str: 设备字符串
        
    Returns:
        device: PyTorch设备
    """
    if device_str == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device_str)
    
    logging.info(f"Using device: {device}")
    return device


def count_parameters(model: torch.nn.Module) -> Dict[str, int]:
    """
    统计模型参数
    
    Args:
        model: PyTorch模型
        
    Returns:
        param_counts: 参数统计字典
    """
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    return {
        'total_parameters': total_params,
        'trainable_parameters': trainable_params,
        'non_trainable_parameters': total_params - trainable_params,
        'trainable_ratio': trainable_params / total_params if total_params > 0 else 0.0
    }


def format_size(size_bytes: int) -> str:
    """
    格式化字节大小
    
    Args:
        size_bytes: 字节数
        
    Returns:
        formatted_size: 格式化的大小字符串
    """
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def get_model_size(model: torch.nn.Module) -> Dict[str, str]:
    """
    获取模型大小信息
    
    Args:
        model: PyTorch模型
        
    Returns:
        size_info: 模型大小信息
    """
    param_size = 0
    buffer_size = 0
    
    for param in model.parameters():
        param_size += param.nelement() * param.element_size()
    
    for buffer in model.buffers():
        buffer_size += buffer.nelement() * buffer.element_size()
    
    total_size = param_size + buffer_size
    
    return {
        'parameter_size': format_size(param_size),
        'buffer_size': format_size(buffer_size),
        'total_size': format_size(total_size)
    }


def log_system_info():
    """记录系统信息"""
    import platform
    import psutil
    
    logging.info("=" * 50)
    logging.info("System Information")
    logging.info("=" * 50)
    logging.info(f"Platform: {platform.platform()}")
    logging.info(f"Python: {platform.python_version()}")
    logging.info(f"PyTorch: {torch.__version__}")
    
    # CPU信息
    logging.info(f"CPU: {platform.processor()}")
    logging.info(f"CPU Cores: {psutil.cpu_count()}")
    
    # 内存信息
    memory = psutil.virtual_memory()
    logging.info(f"Memory: {format_size(memory.total)} (Available: {format_size(memory.available)})")
    
    # GPU信息
    if torch.cuda.is_available():
        logging.info(f"CUDA Available: True")
        logging.info(f"CUDA Version: {torch.version.cuda}")
        logging.info(f"GPU Count: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory
            logging.info(f"GPU {i}: {gpu_name} ({format_size(gpu_memory)})")
    else:
        logging.info("CUDA Available: False")
    
    logging.info("=" * 50)


def create_experiment_dir(base_dir: str, experiment_name: str) -> str:
    """
    创建实验目录
    
    Args:
        base_dir: 基础目录
        experiment_name: 实验名称
        
    Returns:
        experiment_dir: 实验目录路径
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    experiment_dir = os.path.join(base_dir, f"{experiment_name}_{timestamp}")
    
    os.makedirs(experiment_dir, exist_ok=True)
    os.makedirs(os.path.join(experiment_dir, 'checkpoints'), exist_ok=True)
    os.makedirs(os.path.join(experiment_dir, 'logs'), exist_ok=True)
    os.makedirs(os.path.join(experiment_dir, 'results'), exist_ok=True)
    
    logging.info(f"Experiment directory created: {experiment_dir}")
    return experiment_dir


def save_metrics(metrics: Dict[str, Any], filepath: str):
    """
    保存评估指标
    
    Args:
        metrics: 指标字典
        filepath: 保存路径
    """
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    
    # 添加时间戳
    metrics['saved_at'] = datetime.now().isoformat()
    
    with open(filepath, 'w') as f:
        json.dump(metrics, f, indent=2, ensure_ascii=False)
    
    logging.info(f"Metrics saved to {filepath}")


def load_metrics(filepath: str) -> Dict[str, Any]:
    """
    加载评估指标
    
    Args:
        filepath: 指标文件路径
        
    Returns:
        metrics: 指标字典
    """
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"Metrics file not found: {filepath}")
    
    with open(filepath, 'r') as f:
        metrics = json.load(f)
    
    logging.info(f"Metrics loaded from {filepath}")
    return metrics


class EarlyStopping:
    """早停机制"""
    
    def __init__(self, patience: int = 10, min_delta: float = 1e-4, mode: str = 'max'):
        """
        初始化早停机制
        
        Args:
            patience: 耐心值
            min_delta: 最小改善幅度
            mode: 模式 ('max' 或 'min')
        """
        self.patience = patience
        self.min_delta = min_delta
        self.mode = mode
        self.best_score = None
        self.counter = 0
        self.early_stop = False
    
    def __call__(self, score: float) -> bool:
        """
        检查是否应该早停
        
        Args:
            score: 当前分数
            
        Returns:
            early_stop: 是否早停
        """
        if self.best_score is None:
            self.best_score = score
        elif self._is_better(score, self.best_score):
            self.best_score = score
            self.counter = 0
        else:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        
        return self.early_stop
    
    def _is_better(self, current: float, best: float) -> bool:
        """判断当前分数是否更好"""
        if self.mode == 'max':
            return current > best + self.min_delta
        else:
            return current < best - self.min_delta
