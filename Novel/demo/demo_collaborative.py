#!/usr/bin/env python3
"""
LLM-SRec纯正大小模型协同演示脚本

展示纯正的大小模型协同推荐系统：
1. 小模型序列建模
2. 大模型推荐优化
3. 协同机制演示
4. 端到端推荐
"""

import os
import sys
import torch
import yaml
import numpy as np
import logging
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.collaborative.collaborative_model import CollaborativeRecommendationModel
from training.collaborative_trainer import CollaborativeTrainer
from utils.model_utils import setup_logging, set_random_seed

logger = logging.getLogger(__name__)


def demo_model_architecture():
    """演示模型架构"""
    print("\n" + "="*60)
    print("🏗️  模型架构演示")
    print("="*60)
    
    # 简化配置
    config = {
        'device': 'cuda:0' if torch.cuda.is_available() else 'cpu',
        'small_model': {
            'item_num': 1000,
            'hidden_units': 64,
            'num_blocks': 2,
            'num_heads': 1,
            'dropout_rate': 0.2,
            'max_sequence_length': 50
        },
        'large_model': {
            'llm_model': 'llama-3b',
            'load_in_8bit': True
        },
        'collaboration': {
            'distillation_temperature': 4.0
        }
    }
    
    try:
        # 初始化模型
        model = CollaborativeRecommendationModel(config)
        
        print(f"✅ 模型初始化成功")
        print(f"📊 模型参数统计:")
        
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"   - 总参数量: {total_params:,}")
        print(f"   - 可训练参数: {trainable_params:,}")
        print(f"   - 参数效率: {trainable_params/total_params*100:.2f}%")
        
        # 模型组件分析
        small_params = sum(p.numel() for name, p in model.named_parameters() 
                          if any(component in name for component in ['item_emb', 'pos_emb', 'attention_layers', 'feed_forward_layers']))
        large_params = sum(p.numel() for name, p in model.named_parameters() 
                          if any(component in name for component in ['recommendation_optimizer', 'large_output']))
        collab_params = total_params - small_params - large_params
        
        print(f"\n🔍 组件参数分布:")
        print(f"   - 小模型参数: {small_params:,} ({small_params/total_params*100:.1f}%)")
        print(f"   - 大模型参数: {large_params:,} ({large_params/total_params*100:.1f}%)")
        print(f"   - 协同组件参数: {collab_params:,} ({collab_params/total_params*100:.1f}%)")
        
    except Exception as e:
        print(f"❌ 模型初始化失败: {str(e)}")


def demo_forward_pass():
    """演示前向传播"""
    print("\n" + "="*60)
    print("🚀 前向传播演示")
    print("="*60)
    
    config = {
        'device': 'cuda:0' if torch.cuda.is_available() else 'cpu',
        'small_model': {
            'item_num': 1000,
            'hidden_units': 64,
            'num_blocks': 2,
            'num_heads': 1,
            'dropout_rate': 0.2,
            'max_sequence_length': 50
        },
        'large_model': {
            'llm_model': 'llama-3b',
            'load_in_8bit': True
        },
        'collaboration': {
            'distillation_temperature': 4.0
        }
    }
    
    try:
        model = CollaborativeRecommendationModel(config)
        model.eval()
        
        # 模拟输入数据
        batch_size = 4
        seq_len = 20
        
        input_ids = torch.randint(1, 1000, (batch_size, seq_len))
        attention_mask = torch.ones(batch_size, seq_len)
        targets = torch.randint(1, 1000, (batch_size,))
        
        print(f"📥 输入数据:")
        print(f"   - 批次大小: {batch_size}")
        print(f"   - 序列长度: {seq_len}")
        print(f"   - 输入形状: {input_ids.shape}")
        
        # 前向传播
        with torch.no_grad():
            outputs = model(input_ids, attention_mask, targets)
        
        print(f"\n📤 输出结果:")
        print(f"   - 小模型logits形状: {outputs['small_logits'].shape}")
        print(f"   - 大模型logits形状: {outputs['large_logits'].shape}")
        print(f"   - 协同logits形状: {outputs['collaborative_logits'].shape}")
        print(f"   - 用户表示形状: {outputs['user_representation'].shape}")
        print(f"   - 用户μ形状: {outputs['user_mu'].shape}")
        
        print(f"\n💰 损失信息:")
        print(f"   - 总损失: {outputs['total_loss'].item():.4f}")
        print(f"   - 小模型损失: {outputs['small_rec_loss'].item():.4f}")
        print(f"   - 大模型损失: {outputs['large_rec_loss'].item():.4f}")
        print(f"   - 协同损失: {outputs['collaborative_rec_loss'].item():.4f}")
        print(f"   - 蒸馏损失: {outputs['distillation_loss'].item():.4f}")
        print(f"   - 对齐损失: {outputs['alignment_loss'].item():.4f}")
        
    except Exception as e:
        print(f"❌ 前向传播失败: {str(e)}")


def demo_recommendation_generation():
    """演示推荐生成"""
    print("\n" + "="*60)
    print("🎯 推荐生成演示")
    print("="*60)
    
    config = {
        'device': 'cuda:0' if torch.cuda.is_available() else 'cpu',
        'small_model': {
            'item_num': 1000,
            'hidden_units': 64,
            'num_blocks': 2,
            'num_heads': 1,
            'dropout_rate': 0.2,
            'max_sequence_length': 50
        },
        'large_model': {
            'llm_model': 'llama-3b',
            'load_in_8bit': True
        },
        'collaboration': {
            'distillation_temperature': 4.0
        }
    }
    
    try:
        model = CollaborativeRecommendationModel(config)
        model.eval()
        
        # 模拟用户历史交互
        batch_size = 2
        seq_len = 15
        top_k = 10
        
        # 用户1: 喜欢动作电影 (物品ID 100-200)
        user1_history = torch.tensor([120, 135, 142, 156, 167, 178, 189, 195, 0, 0, 0, 0, 0, 0, 0]).unsqueeze(0)
        
        # 用户2: 喜欢喜剧电影 (物品ID 300-400)  
        user2_history = torch.tensor([305, 318, 325, 334, 347, 356, 362, 371, 385, 392, 0, 0, 0, 0, 0]).unsqueeze(0)
        
        input_ids = torch.cat([user1_history, user2_history], dim=0)
        attention_mask = (input_ids != 0).float()
        
        print(f"👤 用户历史交互:")
        print(f"   - 用户1 (动作电影爱好者): {user1_history.squeeze()[:8].tolist()}...")
        print(f"   - 用户2 (喜剧电影爱好者): {user2_history.squeeze()[:10].tolist()}...")
        
        # 生成推荐
        with torch.no_grad():
            recommendations = model.generate_recommendations(input_ids, attention_mask, top_k=top_k)
        
        print(f"\n🎬 推荐结果 (Top-{top_k}):")
        for i, user_recs in enumerate(recommendations):
            print(f"   - 用户{i+1}: {user_recs.tolist()}")
        
        # 分析推荐质量
        print(f"\n📊 推荐分析:")
        user1_recs = recommendations[0].tolist()
        user2_recs = recommendations[1].tolist()
        
        # 检查推荐是否符合用户偏好
        user1_action_count = sum(1 for item in user1_recs if 100 <= item <= 200)
        user2_comedy_count = sum(1 for item in user2_recs if 300 <= item <= 400)
        
        print(f"   - 用户1推荐中动作电影比例: {user1_action_count}/{top_k} ({user1_action_count/top_k*100:.1f}%)")
        print(f"   - 用户2推荐中喜剧电影比例: {user2_comedy_count}/{top_k} ({user2_comedy_count/top_k*100:.1f}%)")
        
    except Exception as e:
        print(f"❌ 推荐生成失败: {str(e)}")


def demo_collaboration_mechanism():
    """演示协同机制"""
    print("\n" + "="*60)
    print("🤝 协同机制演示")
    print("="*60)
    
    config = {
        'device': 'cuda:0' if torch.cuda.is_available() else 'cpu',
        'small_model': {
            'item_num': 1000,
            'hidden_units': 64,
            'num_blocks': 2,
            'num_heads': 1,
            'dropout_rate': 0.2,
            'max_sequence_length': 50
        },
        'large_model': {
            'llm_model': 'llama-3b',
            'load_in_8bit': True
        },
        'collaboration': {
            'distillation_temperature': 4.0
        }
    }
    
    try:
        model = CollaborativeRecommendationModel(config)
        model.eval()
        
        # 模拟输入
        input_ids = torch.randint(1, 1000, (2, 20))
        attention_mask = torch.ones(2, 20)
        
        with torch.no_grad():
            # 小模型输出
            small_outputs = model.small_model_forward(input_ids, attention_mask)
            
            # 大模型输出
            large_outputs = model.large_model_forward(small_outputs['user_mu'])
            
            # 协同输出
            collaborative_outputs = model.collaborative_forward(small_outputs, large_outputs)
        
        print(f"🔍 协同机制分析:")
        
        # 特征对齐效果
        original_features = small_outputs['user_mu']
        aligned_features = collaborative_outputs['aligned_features']
        target_features = large_outputs['optimized_representation']
        
        # 计算对齐相似度
        alignment_similarity = torch.cosine_similarity(aligned_features, target_features, dim=-1)
        
        print(f"   - 特征对齐相似度: {alignment_similarity.mean().item():.4f}")
        
        # 预测一致性
        small_probs = torch.softmax(small_outputs['small_logits'], dim=-1)
        large_probs = torch.softmax(large_outputs['large_logits'], dim=-1)
        collaborative_probs = torch.softmax(collaborative_outputs['collaborative_logits'], dim=-1)
        
        # 计算预测一致性
        small_large_consistency = torch.cosine_similarity(small_probs, large_probs, dim=-1)
        small_collab_consistency = torch.cosine_similarity(small_probs, collaborative_probs, dim=-1)
        large_collab_consistency = torch.cosine_similarity(large_probs, collaborative_probs, dim=-1)
        
        print(f"   - 小-大模型一致性: {small_large_consistency.mean().item():.4f}")
        print(f"   - 小-协同模型一致性: {small_collab_consistency.mean().item():.4f}")
        print(f"   - 大-协同模型一致性: {large_collab_consistency.mean().item():.4f}")
        
        # 知识蒸馏效果
        print(f"\n🧠 知识蒸馏分析:")
        print(f"   - 蒸馏温度: {model.distillation_temperature}")
        print(f"   - 小模型学习大模型知识的程度: {small_large_consistency.mean().item():.4f}")
        
    except Exception as e:
        print(f"❌ 协同机制演示失败: {str(e)}")


def demo_training_preview():
    """演示训练预览"""
    print("\n" + "="*60)
    print("🏋️  训练流程预览")
    print("="*60)
    
    try:
        # 加载配置
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config", "collaborative_config.yaml")
        
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            print(f"✅ 配置文件加载成功")
            print(f"📋 训练配置:")
            print(f"   - 训练轮数: {config['training']['num_epochs']}")
            print(f"   - 批次大小: {config['training']['batch_size']}")
            print(f"   - 学习率: {config['training']['learning_rate']}")
            print(f"   - 早停耐心: {config['training']['patience']}")
            
            print(f"\n🎯 协同配置:")
            print(f"   - 蒸馏温度: {config['collaboration']['distillation_temperature']}")
            print(f"   - 蒸馏权重: {config['collaboration']['distillation_weight']}")
            print(f"   - 对齐权重: {config['collaboration']['alignment_weight']}")
            
            print(f"\n📊 模型配置:")
            print(f"   - 小模型隐藏单元: {config['small_model']['hidden_units']}")
            print(f"   - 小模型层数: {config['small_model']['num_blocks']}")
            print(f"   - 大模型类型: {config['large_model']['llm_model']}")
            
            print(f"\n💡 训练建议:")
            print(f"   - 建议先进行小规模实验验证")
            print(f"   - 可以调整蒸馏温度优化知识传递")
            print(f"   - 监控各组件损失的平衡性")
            
        else:
            print(f"❌ 配置文件未找到: {config_path}")
            
    except Exception as e:
        print(f"❌ 训练预览失败: {str(e)}")


def main():
    """主演示函数"""
    print("🎉 欢迎使用LLM-SRec纯正大小模型协同推荐系统演示！")
    print("\n这个演示将展示系统的主要功能模块：")
    print("1. 模型架构")
    print("2. 前向传播")
    print("3. 推荐生成")
    print("4. 协同机制")
    print("5. 训练预览")
    
    # 设置随机种子
    set_random_seed(42)
    
    # 设置日志
    setup_logging('INFO')
    
    # 运行各个演示
    demo_model_architecture()
    demo_forward_pass()
    demo_recommendation_generation()
    demo_collaboration_mechanism()
    demo_training_preview()
    
    print("\n" + "="*60)
    print("🎊 演示完成！")
    print("="*60)
    print("\n📚 接下来您可以:")
    print("1. 准备真实数据集进行训练")
    print("2. 调整模型配置和超参数")
    print("3. 运行完整的训练流程")
    print("4. 评估模型性能")
    print("\n🚀 开始您的纯正大小模型协同推荐系统研究之旅吧！")


if __name__ == '__main__':
    main()
