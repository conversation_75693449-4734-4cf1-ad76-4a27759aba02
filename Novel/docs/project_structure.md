# Novel 项目结构说明

## 📁 目录结构

```
Novel/
├── README.md                              # 项目主文档
├── requirements.txt                       # Python依赖包
│
├── config/                              # 配置文件
│   └── collaborative_config.yaml       # 端云协同配置
│
├── models/                              # 模型实现
│   ├── client/                         # 端侧模型
│   │   └── privacy_protection.py      # 隐私保护模块 ⭐
│   │
│   ├── cloud/                          # 云端模型
│   │   └── llm_cloud_server.py        # LLM云端服务器
│   │
│   ├── knowledge_distillation/         # 知识蒸馏
│   │   └── distillation_engine.py     # 蒸馏引擎
│   │
│   └── collaborative/                  # 协同模型
│
├── system/                             # 系统整合
│   └── edge_cloud_system.py           # 端云协同系统 ⭐
│
├── training/                           # 训练模块
│   └── collaborative_trainer.py       # 协同训练器
│
├── utils/                              # 工具模块
│   ├── communication.py              # 端云通信工具 ⭐
│   └── model_utils.py                 # 模型工具
│
├── demo/                               # 演示脚本
│   └── demo_edge_cloud.py            # 端云协同演示 ⭐
│
└── docs/                               # 文档目录
    ├── project_structure.md           # 项目结构说明
    └── project_summary.md             # 项目总结
```

**⭐ 标记的文件是Novel项目的核心创新模块**

## 🔧 核心模块说明

### 端侧模型 (models/client/)
- **cf_srec_client.py**: 轻量级CF-SRec模型，负责序列建模和用户表示生成
- **privacy_protection.py**: 隐私保护机制，确保原始数据不离端侧

### 云端模型 (models/cloud/)
- **llm_cloud_server.py**: LLM云端服务器，负责推荐优化和Top-K生成
- **sequence_enhancement.py**: 序列理解增强模块，提升LLM序列建模能力

### 知识蒸馏 (models/knowledge_distillation/)
- **distillation_engine.py**: 知识蒸馏引擎，实现云端知识向端侧迁移
- **knowledge_transfer.py**: 知识迁移策略，包括特征、注意力、响应蒸馏

### 系统整合 (system/)
- **edge_cloud_system.py**: 端云协同系统主入口，协调各模块工作

### 训练模块 (training/)
- **collaborative_trainer.py**: 端云协同训练器，实现联合优化
- **distillation_trainer.py**: 知识蒸馏训练器，专门处理蒸馏过程

## 🚀 快速开始

### 1. 环境配置
```bash
# 安装依赖
pip install -r requirements.txt

# 配置环境变量
export NOVEL_CONFIG_PATH=./config/collaborative_config.yaml
```

### 2. 运行演示
```bash
# 端云协同演示
python demo/demo_edge_cloud.py

# 隐私保护演示
python demo/demo_privacy_protection.py

# 性能测试
python demo/demo_performance.py
```

### 3. 训练模型
```bash
# 端云协同训练
python scripts/train_edge_cloud.py --config config/collaborative_config.yaml

# 模型评估
python scripts/evaluate_model.py --model_path ./checkpoints/best_model
```

## 📊 配置说明

主要配置文件 `config/collaborative_config.yaml` 包含：

- **端侧配置**: CF-SRec模型参数、隐私保护设置
- **云端配置**: LLM模型选择、序列增强参数
- **协同配置**: 知识蒸馏策略、通信协议
- **训练配置**: 学习率、批次大小、优化器设置

## 🔒 隐私保护

Novel项目内置多层隐私保护机制：

1. **本地处理**: 原始用户数据仅在端侧处理
2. **表示传输**: 仅传输64维抽象用户表示
3. **差分隐私**: 可选的噪声注入保护
4. **安全通信**: 端云通信采用加密协议

## 📈 性能监控

项目提供完整的性能监控工具：

- **推荐精度**: NDCG@K, HR@K, MRR等指标
- **系统效率**: 响应时间、吞吐量、资源消耗
- **隐私保护**: 隐私泄露风险评估
- **通信开销**: 数据传输量、网络延迟

## 🤝 贡献指南

欢迎社区贡献！请参考以下流程：

1. Fork项目并创建特性分支
2. 遵循代码规范和注释要求
3. 添加相应的测试用例
4. 提交Pull Request并描述改动

## 📞 技术支持

- **问题反馈**: [GitHub Issues](https://github.com/your-repo/Novel/issues)
- **技术讨论**: [Discussions](https://github.com/your-repo/Novel/discussions)
- **邮件联系**: <EMAIL>
