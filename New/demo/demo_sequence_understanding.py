#!/usr/bin/env python3
"""
LLM序列理解能力演示脚本

展示增强版LLM-SRec-Pure如何保留和提升LLM的序列理解能力：
1. 特殊Token嵌入机制
2. 序列-文本混合表示
3. 均匀性正则化效果
4. 序列模式识别能力
5. 知识蒸馏对齐效果
"""

import os
import sys
import torch
import torch.nn.functional as F
import numpy as np
from typing import Dict, Any, List

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.collaborative.enhanced_collaborative_model import EnhancedCollaborativeModel
from utils.model_utils import setup_logging, set_random_seed


def demo_special_token_mechanism():
    """演示特殊Token嵌入机制"""
    print("\n" + "="*60)
    print("🏷️  特殊Token嵌入机制演示")
    print("="*60)
    
    # 简化配置
    config = {
        'device': 'cuda:0' if torch.cuda.is_available() else 'cpu',
        'small_model': {
            'item_num': 1000,
            'hidden_units': 64,
            'num_blocks': 2,
            'num_heads': 1,
            'dropout_rate': 0.2,
            'max_sequence_length': 50
        },
        'large_model': {
            'llm_model': 'llama-3b',
            'load_in_8bit': True
        },
        'collaboration': {
            'distillation_temperature': 4.0,
            'uniformity_weight': 0.1
        }
    }
    
    try:
        # 初始化增强模型
        model = EnhancedCollaborativeModel(config)
        
        print(f"✅ 增强模型初始化成功")
        
        # 检查特殊Token
        special_tokens = [
            '[UserRep]', '[HistoryEmb]', '[UserOut]', 
            '[ItemOut]', '[SeqStart]', '[SeqEnd]'
        ]
        
        print(f"\n🏷️  特殊Token检查:")
        for token in special_tokens:
            token_id = model.llm_tokenizer.convert_tokens_to_ids(token)
            print(f"   - {token}: ID={token_id}")
        
        # 演示文本提示生成
        print(f"\n📝 文本提示生成演示:")
        
        # 模拟用户序列
        user_sequences = torch.tensor([
            [120, 135, 142, 156, 167, 178, 189, 195, 0, 0],  # 动作电影爱好者
            [305, 318, 325, 334, 347, 356, 362, 371, 0, 0],  # 喜剧电影爱好者
        ])
        
        prompts = model.generate_text_prompt(user_sequences)
        
        for i, prompt in enumerate(prompts):
            print(f"\n   用户{i+1}的提示:")
            print(f"   {prompt}")
        
        # 分词演示
        print(f"\n🔤 分词结果演示:")
        sample_prompt = prompts[0]
        tokens = model.llm_tokenizer(sample_prompt, return_tensors="pt")
        
        print(f"   - 原始文本长度: {len(sample_prompt)}")
        print(f"   - Token数量: {tokens['input_ids'].size(1)}")
        print(f"   - Token IDs: {tokens['input_ids'][0][:20].tolist()}...")
        
        # 检查特殊Token位置
        for token in ['[SeqStart]', '[HistoryEmb]', '[UserOut]']:
            token_id = model.llm_tokenizer.convert_tokens_to_ids(token)
            positions = (tokens['input_ids'][0] == token_id).nonzero(as_tuple=True)[0]
            if len(positions) > 0:
                print(f"   - {token} 位置: {positions.tolist()}")
        
    except Exception as e:
        print(f"❌ 特殊Token机制演示失败: {str(e)}")


def demo_sequence_text_fusion():
    """演示序列-文本混合表示"""
    print("\n" + "="*60)
    print("🔗 序列-文本混合表示演示")
    print("="*60)
    
    config = {
        'device': 'cuda:0' if torch.cuda.is_available() else 'cpu',
        'small_model': {
            'item_num': 1000,
            'hidden_units': 64,
            'num_blocks': 2,
            'num_heads': 1,
            'dropout_rate': 0.2,
            'max_sequence_length': 50
        },
        'large_model': {
            'llm_model': 'llama-3b',
            'load_in_8bit': True
        },
        'collaboration': {
            'distillation_temperature': 4.0,
            'uniformity_weight': 0.1
        }
    }
    
    try:
        model = EnhancedCollaborativeModel(config)
        model.eval()
        
        # 模拟用户序列
        batch_size = 2
        seq_len = 15
        
        input_ids = torch.randint(1, 1000, (batch_size, seq_len))
        attention_mask = torch.ones(batch_size, seq_len)
        
        print(f"📊 输入数据:")
        print(f"   - 批次大小: {batch_size}")
        print(f"   - 序列长度: {seq_len}")
        print(f"   - 用户序列示例: {input_ids[0][:10].tolist()}")
        
        with torch.no_grad():
            # 小模型前向传播
            small_outputs = model.small_model_forward(input_ids, attention_mask)
            
            print(f"\n🔍 小模型输出:")
            print(f"   - 隐藏状态形状: {small_outputs['hidden_states'].shape}")
            print(f"   - 用户表示形状: {small_outputs['user_representation'].shape}")
            print(f"   - 注意力权重形状: {small_outputs['attention_weights'].shape}")
            
            # 序列历史投影
            history_emb = model.history_projector(small_outputs['user_representation'])
            
            print(f"\n🎯 序列历史投影:")
            print(f"   - 原始维度: {small_outputs['user_representation'].shape[1]}")
            print(f"   - 投影后维度: {history_emb.shape[1]}")
            print(f"   - 维度扩展比例: {history_emb.shape[1] / small_outputs['user_representation'].shape[1]:.1f}x")
            
            # 分析序列表示的质量
            user_repr = small_outputs['user_representation']
            
            # 计算表示的多样性
            pairwise_sim = F.cosine_similarity(user_repr.unsqueeze(1), user_repr.unsqueeze(0), dim=-1)
            avg_similarity = pairwise_sim.mean().item()
            
            print(f"\n📈 序列表示质量分析:")
            print(f"   - 表示范数: {torch.norm(user_repr, dim=-1).mean().item():.4f}")
            print(f"   - 平均相似度: {avg_similarity:.4f}")
            print(f"   - 表示多样性: {1 - avg_similarity:.4f}")
            
    except Exception as e:
        print(f"❌ 序列-文本融合演示失败: {str(e)}")


def demo_uniformity_regularization():
    """演示均匀性正则化效果"""
    print("\n" + "="*60)
    print("⚖️  均匀性正则化演示")
    print("="*60)
    
    # 模拟不同质量的表示
    batch_size = 16
    hidden_dim = 128
    
    print(f"📊 实验设置:")
    print(f"   - 批次大小: {batch_size}")
    print(f"   - 表示维度: {hidden_dim}")
    
    # 场景1: 随机表示（理想情况）
    random_embeddings = torch.randn(batch_size, hidden_dim)
    random_embeddings = F.normalize(random_embeddings, dim=-1)
    
    # 场景2: 坍塌表示（问题情况）
    collapsed_embeddings = torch.ones(batch_size, hidden_dim) + torch.randn(batch_size, hidden_dim) * 0.1
    collapsed_embeddings = F.normalize(collapsed_embeddings, dim=-1)
    
    # 场景3: 部分坍塌表示
    partial_collapsed = torch.randn(batch_size, hidden_dim)
    partial_collapsed[:8] = partial_collapsed[:8] * 0.1 + torch.ones(8, hidden_dim)  # 一半坍塌
    partial_collapsed = F.normalize(partial_collapsed, dim=-1)
    
    def uniformity_loss(embeddings, t=2.0):
        """计算均匀性损失"""
        embeddings = F.normalize(embeddings, dim=-1)
        return torch.pdist(embeddings, p=2).pow(2).mul(-t).exp().mean().log()
    
    def analyze_distribution(embeddings, name):
        """分析表示分布"""
        # 均匀性损失
        unif_loss = uniformity_loss(embeddings).item()
        
        # 成对距离统计
        distances = torch.pdist(embeddings, p=2)
        min_dist = distances.min().item()
        max_dist = distances.max().item()
        mean_dist = distances.mean().item()
        std_dist = distances.std().item()
        
        # 相似度统计
        similarities = F.cosine_similarity(embeddings.unsqueeze(1), embeddings.unsqueeze(0), dim=-1)
        # 去除对角线（自相似度）
        mask = ~torch.eye(similarities.size(0), dtype=bool)
        off_diag_sim = similarities[mask]
        mean_sim = off_diag_sim.mean().item()
        std_sim = off_diag_sim.std().item()
        
        print(f"\n   {name}:")
        print(f"     - 均匀性损失: {unif_loss:.4f}")
        print(f"     - 平均距离: {mean_dist:.4f} ± {std_dist:.4f}")
        print(f"     - 距离范围: [{min_dist:.4f}, {max_dist:.4f}]")
        print(f"     - 平均相似度: {mean_sim:.4f} ± {std_sim:.4f}")
        
        return unif_loss
    
    print(f"\n📈 不同表示质量的均匀性分析:")
    
    # 分析三种情况
    random_loss = analyze_distribution(random_embeddings, "随机表示（理想）")
    collapsed_loss = analyze_distribution(collapsed_embeddings, "坍塌表示（问题）")
    partial_loss = analyze_distribution(partial_collapsed, "部分坍塌表示")
    
    print(f"\n💡 均匀性正则化原理:")
    print(f"   - 均匀性损失越小越好（负值，绝对值越小越好）")
    print(f"   - 随机表示损失: {random_loss:.4f} （最好）")
    print(f"   - 部分坍塌损失: {partial_loss:.4f} （中等）")
    print(f"   - 完全坍塌损失: {collapsed_loss:.4f} （最差）")
    print(f"   - 正则化项鼓励表示在球面上均匀分布")


def demo_sequence_pattern_recognition():
    """演示序列模式识别能力"""
    print("\n" + "="*60)
    print("🔍 序列模式识别能力演示")
    print("="*60)
    
    # 创建具有明显模式的序列
    def create_pattern_sequences():
        """创建具有不同模式的用户序列"""
        sequences = []
        patterns = []
        
        # 模式1: 周期性购买（每隔3个物品重复）
        periodic_seq = []
        base_items = [100, 101, 102]
        for i in range(15):
            periodic_seq.append(base_items[i % 3] + (i // 3) * 10)
        sequences.append(periodic_seq)
        patterns.append("周期性购买模式")
        
        # 模式2: 递增趋势（物品ID逐渐增大）
        increasing_seq = list(range(200, 215))
        sequences.append(increasing_seq)
        patterns.append("递增趋势模式")
        
        # 模式3: 聚类模式（在几个类别间跳跃）
        cluster_seq = []
        clusters = [[300, 301, 302], [400, 401, 402], [500, 501, 502]]
        for i in range(15):
            cluster_idx = i % 3
            item_idx = (i // 3) % 3
            cluster_seq.append(clusters[cluster_idx][item_idx])
        sequences.append(cluster_seq)
        patterns.append("聚类跳跃模式")
        
        # 模式4: 随机模式（对照组）
        random_seq = np.random.randint(600, 700, 15).tolist()
        sequences.append(random_seq)
        patterns.append("随机模式（对照）")
        
        return sequences, patterns
    
    sequences, patterns = create_pattern_sequences()
    
    print(f"📊 序列模式数据:")
    for i, (seq, pattern) in enumerate(zip(sequences, patterns)):
        print(f"   - 序列{i+1} ({pattern}): {seq[:10]}...")
    
    # 分析序列的统计特性
    def analyze_sequence_statistics(sequences, patterns):
        """分析序列的统计特性"""
        print(f"\n📈 序列统计特性分析:")
        
        for i, (seq, pattern) in enumerate(zip(sequences, patterns)):
            seq_array = np.array(seq)
            
            # 基本统计
            mean_val = seq_array.mean()
            std_val = seq_array.std()
            
            # 趋势分析（线性回归斜率）
            x = np.arange(len(seq))
            slope = np.polyfit(x, seq_array, 1)[0]
            
            # 周期性分析（自相关）
            def autocorrelation(x, lag):
                if lag >= len(x):
                    return 0
                return np.corrcoef(x[:-lag], x[lag:])[0, 1] if lag > 0 else 1.0
            
            # 计算不同lag的自相关
            autocorr_lag1 = autocorrelation(seq_array, 1)
            autocorr_lag3 = autocorrelation(seq_array, 3)
            
            # 变化率
            changes = np.diff(seq_array)
            change_rate = np.std(changes)
            
            print(f"\n   序列{i+1} ({pattern}):")
            print(f"     - 均值: {mean_val:.2f}, 标准差: {std_val:.2f}")
            print(f"     - 趋势斜率: {slope:.2f}")
            print(f"     - 自相关(lag=1): {autocorr_lag1:.3f}")
            print(f"     - 自相关(lag=3): {autocorr_lag3:.3f}")
            print(f"     - 变化率: {change_rate:.2f}")
    
    analyze_sequence_statistics(sequences, patterns)
    
    print(f"\n💡 序列模式识别原理:")
    print(f"   - 周期性模式：高自相关性，特定lag处有峰值")
    print(f"   - 趋势模式：明显的正/负斜率")
    print(f"   - 聚类模式：中等自相关性，规律性跳跃")
    print(f"   - 随机模式：低自相关性，高变化率")
    print(f"   - LLM通过特殊Token机制能够学习这些模式")


def demo_knowledge_distillation_alignment():
    """演示知识蒸馏对齐效果"""
    print("\n" + "="*60)
    print("🎓 知识蒸馏对齐效果演示")
    print("="*60)
    
    # 模拟教师（CF-SRec）和学生（LLM）的表示
    batch_size = 8
    cf_dim = 64
    llm_dim = 128
    
    print(f"📊 蒸馏设置:")
    print(f"   - 批次大小: {batch_size}")
    print(f"   - CF表示维度: {cf_dim}")
    print(f"   - LLM表示维度: {llm_dim}")
    
    # 教师表示（CF-SRec，有明确的结构）
    teacher_repr = torch.randn(batch_size, cf_dim)
    # 让教师表示有一定的聚类结构
    teacher_repr[:4] += torch.tensor([2.0] + [0.0] * (cf_dim-1))  # 聚类1
    teacher_repr[4:] += torch.tensor([0.0, 2.0] + [0.0] * (cf_dim-2))  # 聚类2
    
    # 学生表示（LLM，初始时较随机）
    student_repr = torch.randn(batch_size, llm_dim)
    
    # 对齐网络
    alignment_net = torch.nn.Sequential(
        torch.nn.Linear(llm_dim, cf_dim),
        torch.nn.LayerNorm(cf_dim),
        torch.nn.ReLU()
    )
    
    # 模拟训练过程
    optimizer = torch.optim.Adam(alignment_net.parameters(), lr=0.01)
    
    print(f"\n🎯 蒸馏训练过程:")
    
    for epoch in range(5):
        # 对齐学生表示
        aligned_student = alignment_net(student_repr)
        
        # 归一化
        teacher_norm = F.normalize(teacher_repr, p=2, dim=1)
        student_norm = F.normalize(aligned_student, p=2, dim=1)
        
        # 对齐损失
        mse_loss = F.mse_loss(student_norm, teacher_norm)
        
        # 均匀性损失
        def uniformity_loss(embeddings, t=2.0):
            embeddings = F.normalize(embeddings, dim=-1)
            return torch.pdist(embeddings, p=2).pow(2).mul(-t).exp().mean().log()
        
        teacher_uniformity = uniformity_loss(teacher_norm)
        student_uniformity = uniformity_loss(student_norm)
        
        # 总损失
        total_loss = mse_loss + 0.1 * (teacher_uniformity + student_uniformity)
        
        # 反向传播
        optimizer.zero_grad()
        total_loss.backward()
        optimizer.step()
        
        # 计算对齐质量
        cosine_sim = F.cosine_similarity(teacher_norm, student_norm, dim=1).mean()
        
        print(f"   Epoch {epoch+1}: 损失={total_loss.item():.4f}, "
              f"MSE={mse_loss.item():.4f}, 余弦相似度={cosine_sim.item():.4f}")
    
    # 最终对齐效果分析
    with torch.no_grad():
        final_aligned = alignment_net(student_repr)
        final_teacher_norm = F.normalize(teacher_repr, p=2, dim=1)
        final_student_norm = F.normalize(final_aligned, p=2, dim=1)
        
        # 计算最终指标
        final_mse = F.mse_loss(final_student_norm, final_teacher_norm)
        final_cosine = F.cosine_similarity(final_teacher_norm, final_student_norm, dim=1)
        
        print(f"\n📈 最终对齐效果:")
        print(f"   - 最终MSE损失: {final_mse.item():.4f}")
        print(f"   - 平均余弦相似度: {final_cosine.mean().item():.4f}")
        print(f"   - 相似度标准差: {final_cosine.std().item():.4f}")
        print(f"   - 最高相似度: {final_cosine.max().item():.4f}")
        print(f"   - 最低相似度: {final_cosine.min().item():.4f}")
    
    print(f"\n💡 知识蒸馏对齐原理:")
    print(f"   - MSE损失确保表示在数值上接近")
    print(f"   - 余弦相似度确保表示在方向上一致")
    print(f"   - 均匀性正则化防止表示坍塌")
    print(f"   - 通过对齐，LLM学习到CF-SRec的序列理解能力")


def main():
    """主演示函数"""
    print("🧠 LLM序列理解能力演示")
    print("=" * 60)
    print("\n本演示将展示增强版LLM-SRec-Pure如何保留和提升LLM的序列理解能力：")
    print("1. 特殊Token嵌入机制")
    print("2. 序列-文本混合表示")
    print("3. 均匀性正则化效果")
    print("4. 序列模式识别能力")
    print("5. 知识蒸馏对齐效果")
    
    # 设置随机种子
    set_random_seed(42)
    
    # 设置日志
    setup_logging('INFO')
    
    # 运行各个演示
    demo_special_token_mechanism()
    demo_sequence_text_fusion()
    demo_uniformity_regularization()
    demo_sequence_pattern_recognition()
    demo_knowledge_distillation_alignment()
    
    print("\n" + "="*60)
    print("🎊 LLM序列理解能力演示完成！")
    print("="*60)
    print("\n📚 核心技术总结:")
    print("1. 特殊Token机制：建立序列与文本的桥梁")
    print("2. 序列注入：将CF-SRec表示直接注入LLM")
    print("3. 均匀性正则化：防止表示学习退化")
    print("4. 模式识别：LLM学习序列的内在规律")
    print("5. 知识蒸馏：实现大小模型的有效对齐")
    print("\n🚀 这些技术共同提升了LLM的序列理解能力！")


if __name__ == '__main__':
    main()
