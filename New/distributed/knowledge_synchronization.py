"""
分布式知识同步机制
实现云端LLM知识向多个客户端的分布式同步
"""

import asyncio
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Any, Optional, Set
import logging
import time
import hashlib
import pickle
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class SyncStrategy(Enum):
    """同步策略"""
    BROADCAST = "broadcast"  # 广播到所有客户端
    SELECTIVE = "selective"  # 选择性同步
    GRADUAL = "gradual"     # 渐进式同步
    PRIORITY = "priority"   # 优先级同步


@dataclass
class KnowledgeUpdate:
    """知识更新包"""
    update_id: str
    version: str
    timestamp: float
    knowledge_type: str  # 'model_weights', 'embeddings', 'attention_patterns'
    data: Dict[str, torch.Tensor]
    target_clients: Set[str] = field(default_factory=set)
    priority: int = 1
    checksum: str = ""
    
    def __post_init__(self):
        if not self.checksum:
            self.checksum = self._compute_checksum()
    
    def _compute_checksum(self) -> str:
        """计算数据校验和"""
        data_bytes = pickle.dumps(self.data)
        return hashlib.md5(data_bytes).hexdigest()


class DistributedKnowledgeSync:
    """分布式知识同步器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.sync_strategy = SyncStrategy(config.get('sync_strategy', 'selective'))
        self.sync_interval = config.get('sync_interval', 300)  # 5分钟
        self.max_pending_updates = config.get('max_pending_updates', 100)
        
        # 知识存储
        self.knowledge_store = KnowledgeStore()
        self.pending_updates = {}  # client_id -> List[KnowledgeUpdate]
        self.client_versions = {}  # client_id -> version
        self.sync_history = {}     # update_id -> sync_status
        
        # 同步状态
        self.is_syncing = False
        self.last_sync_time = 0
        self.total_syncs = 0
        
        logger.info(f"DistributedKnowledgeSync initialized with strategy: {self.sync_strategy.value}")
    
    async def start_sync_service(self):
        """启动同步服务"""
        self.is_syncing = True
        
        # 启动定期同步任务
        asyncio.create_task(self._periodic_sync_loop())
        
        # 启动同步状态监控
        asyncio.create_task(self._sync_monitoring_loop())
        
        logger.info("Knowledge synchronization service started")
    
    async def register_client_for_sync(self, client_id: str, client_info: Dict[str, Any]):
        """注册客户端进行同步"""
        self.client_versions[client_id] = client_info.get('current_version', '0.0')
        self.pending_updates[client_id] = []
        
        logger.info(f"Client {client_id} registered for knowledge sync, "
                   f"current version: {self.client_versions[client_id]}")
    
    async def generate_knowledge_update(self, cloud_outputs: Dict[str, torch.Tensor], 
                                      update_type: str = 'model_weights') -> KnowledgeUpdate:
        """从云端输出生成知识更新"""
        update_id = f"update_{int(time.time() * 1000)}"
        version = f"v{time.strftime('%Y%m%d_%H%M%S')}"
        
        # 提取关键知识
        knowledge_data = {}
        
        if update_type == 'model_weights':
            # 提取模型权重更新
            if 'optimized_repr' in cloud_outputs:
                knowledge_data['user_projector_weights'] = cloud_outputs['optimized_repr']
            
        elif update_type == 'embeddings':
            # 提取嵌入更新
            if 'item_embeddings' in cloud_outputs:
                knowledge_data['item_embeddings'] = cloud_outputs['item_embeddings']
                
        elif update_type == 'attention_patterns':
            # 提取注意力模式
            if 'attention_weights' in cloud_outputs:
                knowledge_data['attention_patterns'] = cloud_outputs['attention_weights']
        
        update = KnowledgeUpdate(
            update_id=update_id,
            version=version,
            timestamp=time.time(),
            knowledge_type=update_type,
            data=knowledge_data,
            priority=self._compute_update_priority(knowledge_data)
        )
        
        # 存储到知识库
        await self.knowledge_store.store_update(update)
        
        logger.info(f"Generated knowledge update {update_id} of type {update_type}")
        return update
    
    def _compute_update_priority(self, knowledge_data: Dict[str, torch.Tensor]) -> int:
        """计算更新优先级"""
        # 基于数据变化幅度计算优先级
        total_change = 0.0
        for key, tensor in knowledge_data.items():
            if tensor.numel() > 0:
                total_change += torch.norm(tensor).item()
        
        # 变化越大，优先级越高
        if total_change > 10.0:
            return 1  # 高优先级
        elif total_change > 1.0:
            return 2  # 中优先级
        else:
            return 3  # 低优先级
    
    async def schedule_sync_to_clients(self, update: KnowledgeUpdate, 
                                     target_clients: Optional[List[str]] = None):
        """调度同步到客户端"""
        if target_clients is None:
            target_clients = list(self.client_versions.keys())
        
        # 根据同步策略选择目标客户端
        selected_clients = self._select_clients_by_strategy(target_clients, update)
        
        # 添加到待同步队列
        for client_id in selected_clients:
            if len(self.pending_updates[client_id]) < self.max_pending_updates:
                self.pending_updates[client_id].append(update)
                update.target_clients.add(client_id)
            else:
                logger.warning(f"Client {client_id} has too many pending updates, skipping")
        
        logger.info(f"Scheduled update {update.update_id} to {len(selected_clients)} clients")
    
    def _select_clients_by_strategy(self, candidates: List[str], 
                                  update: KnowledgeUpdate) -> List[str]:
        """根据策略选择客户端"""
        if self.sync_strategy == SyncStrategy.BROADCAST:
            return candidates
        
        elif self.sync_strategy == SyncStrategy.SELECTIVE:
            # 选择版本较旧的客户端
            threshold_time = time.time() - 3600  # 1小时前
            return [c for c in candidates if self._get_client_last_sync(c) < threshold_time]
        
        elif self.sync_strategy == SyncStrategy.PRIORITY:
            # 根据更新优先级选择
            if update.priority <= 2:  # 高/中优先级
                return candidates
            else:  # 低优先级，只选择一半客户端
                return candidates[:len(candidates)//2]
        
        elif self.sync_strategy == SyncStrategy.GRADUAL:
            # 渐进式同步，每次只选择部分客户端
            batch_size = max(1, len(candidates) // 3)
            return candidates[:batch_size]
        
        return candidates
    
    def _get_client_last_sync(self, client_id: str) -> float:
        """获取客户端最后同步时间"""
        # 简化实现，返回当前时间减去随机值
        return time.time() - np.random.uniform(0, 7200)  # 0-2小时前
    
    async def _periodic_sync_loop(self):
        """定期同步循环"""
        while self.is_syncing:
            try:
                await asyncio.sleep(self.sync_interval)
                await self._execute_pending_syncs()
                
            except Exception as e:
                logger.error(f"Error in periodic sync loop: {e}")
    
    async def _execute_pending_syncs(self):
        """执行待同步更新"""
        if not any(self.pending_updates.values()):
            return
        
        sync_tasks = []
        for client_id, updates in self.pending_updates.items():
            if updates:
                task = self._sync_to_single_client(client_id, updates)
                sync_tasks.append(task)
        
        if sync_tasks:
            await asyncio.gather(*sync_tasks, return_exceptions=True)
            self.total_syncs += 1
            self.last_sync_time = time.time()
    
    async def _sync_to_single_client(self, client_id: str, updates: List[KnowledgeUpdate]):
        """同步到单个客户端"""
        try:
            # 按优先级排序
            updates.sort(key=lambda x: x.priority)
            
            for update in updates:
                # 模拟网络传输
                await asyncio.sleep(0.1)
                
                # 验证数据完整性
                if self._verify_update_integrity(update):
                    # 记录同步成功
                    self.sync_history[update.update_id] = {
                        'client_id': client_id,
                        'status': 'success',
                        'timestamp': time.time()
                    }
                    
                    # 更新客户端版本
                    self.client_versions[client_id] = update.version
                    
                    logger.info(f"✅ Synced update {update.update_id} to client {client_id}")
                else:
                    logger.error(f"❌ Failed to verify update {update.update_id} for client {client_id}")
            
            # 清空已同步的更新
            self.pending_updates[client_id] = []
            
        except Exception as e:
            logger.error(f"Error syncing to client {client_id}: {e}")
    
    def _verify_update_integrity(self, update: KnowledgeUpdate) -> bool:
        """验证更新完整性"""
        current_checksum = update._compute_checksum()
        return current_checksum == update.checksum
    
    async def _sync_monitoring_loop(self):
        """同步监控循环"""
        while self.is_syncing:
            await asyncio.sleep(60)  # 每分钟监控一次
            
            total_pending = sum(len(updates) for updates in self.pending_updates.values())
            active_clients = len([c for c in self.client_versions.keys() 
                                if self._get_client_last_sync(c) > time.time() - 1800])
            
            logger.info(f"📊 Sync Monitor - Active Clients: {active_clients}, "
                       f"Pending Updates: {total_pending}, "
                       f"Total Syncs: {self.total_syncs}")
    
    def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态"""
        return {
            'is_syncing': self.is_syncing,
            'sync_strategy': self.sync_strategy.value,
            'total_clients': len(self.client_versions),
            'total_syncs': self.total_syncs,
            'last_sync_time': self.last_sync_time,
            'pending_updates': {k: len(v) for k, v in self.pending_updates.items()}
        }


class KnowledgeStore:
    """知识存储"""
    
    def __init__(self):
        self.updates = {}  # update_id -> KnowledgeUpdate
        self.versions = {}  # version -> List[update_id]
    
    async def store_update(self, update: KnowledgeUpdate):
        """存储知识更新"""
        self.updates[update.update_id] = update
        
        if update.version not in self.versions:
            self.versions[update.version] = []
        self.versions[update.version].append(update.update_id)
    
    async def get_update(self, update_id: str) -> Optional[KnowledgeUpdate]:
        """获取知识更新"""
        return self.updates.get(update_id)


# 演示分布式知识同步
async def demo_distributed_knowledge_sync():
    """演示分布式知识同步"""
    print("🚀 启动分布式知识同步演示...")
    
    # 创建同步器
    config = {
        'sync_strategy': 'selective',
        'sync_interval': 10,  # 10秒同步一次（演示用）
        'max_pending_updates': 50
    }
    sync_service = DistributedKnowledgeSync(config)
    
    # 启动同步服务
    await sync_service.start_sync_service()
    
    # 注册客户端
    clients = ['client_1', 'client_2', 'client_3', 'client_4', 'client_5']
    for client_id in clients:
        await sync_service.register_client_for_sync(client_id, {'current_version': '1.0'})
    
    # 生成知识更新
    cloud_outputs = {
        'optimized_repr': torch.randn(64, 128),
        'item_embeddings': torch.randn(1000, 64),
        'attention_weights': torch.randn(8, 64, 64)
    }
    
    # 生成不同类型的更新
    for update_type in ['model_weights', 'embeddings', 'attention_patterns']:
        update = await sync_service.generate_knowledge_update(cloud_outputs, update_type)
        await sync_service.schedule_sync_to_clients(update)
    
    # 等待同步完成
    await asyncio.sleep(15)
    
    # 显示同步状态
    status = sync_service.get_sync_status()
    print(f"📊 同步状态: {status}")
    
    print("✅ 分布式知识同步演示完成")


if __name__ == "__main__":
    asyncio.run(demo_distributed_knowledge_sync())
