"""
增强版LLM-SRec协同推荐系统

保留并增强LLM的序列理解能力：
1. 特殊Token嵌入机制
2. 序列-文本混合表示
3. 均匀性正则化
4. 两阶段知识蒸馏
5. 序列位置感知注意力
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import LoraConfig, get_peft_model, PeftModel, prepare_model_for_kbit_training

logger = logging.getLogger(__name__)


class EnhancedCollaborativeModel(nn.Module):
    """
    增强版大小模型协同推荐系统
    
    核心改进：
    1. 保留LLM-SRec的序列理解能力
    2. 增强特殊Token处理机制
    3. 序列-文本混合表示学习
    4. 均匀性正则化防止表示坍塌
    5. 位置感知的序列建模
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.device = config.get('device', 'cuda:0')
        
        # 初始化小模型组件（CF-SRec）
        self._init_small_model()
        
        # 初始化大模型组件（增强版LLM）
        self._init_enhanced_large_model()
        
        # 初始化协同机制
        self._init_enhanced_collaboration()
        
        # 初始化损失函数
        self._init_loss_functions()
        
        logger.info("EnhancedCollaborativeModel initialized successfully")
    
    def _init_small_model(self):
        """初始化小模型组件（CF-SRec）"""
        small_config = self.config['small_model']
        
        # CF-SRec小模型参数
        self.item_num = small_config.get('item_num', 10000)
        self.hidden_units = small_config.get('hidden_units', 64)
        self.num_blocks = small_config.get('num_blocks', 2)
        self.num_heads = small_config.get('num_heads', 1)
        self.dropout_rate = small_config.get('dropout_rate', 0.2)
        self.maxlen = small_config.get('max_sequence_length', 128)
        
        # 物品嵌入层
        self.item_emb = nn.Embedding(self.item_num + 1, self.hidden_units, padding_idx=0)
        self.pos_emb = nn.Embedding(self.maxlen, self.hidden_units)
        
        # 多头自注意力层（增强版）
        self.attention_layers = nn.ModuleList([
            nn.MultiheadAttention(
                embed_dim=self.hidden_units,
                num_heads=self.num_heads,
                dropout=self.dropout_rate,
                batch_first=True
            ) for _ in range(self.num_blocks)
        ])
        
        # 前馈网络
        self.feed_forward_layers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(self.hidden_units, self.hidden_units * 4),
                nn.GELU(),
                nn.Dropout(self.dropout_rate),
                nn.Linear(self.hidden_units * 4, self.hidden_units),
                nn.Dropout(self.dropout_rate)
            ) for _ in range(self.num_blocks)
        ])
        
        # 层归一化
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(self.hidden_units, eps=1e-8) for _ in range(self.num_blocks * 2)
        ])
        
        self.last_layernorm = nn.LayerNorm(self.hidden_units, eps=1e-8)
        
        logger.info(f"Small CF-SRec model initialized with {self.hidden_units} hidden units")
    
    def _init_enhanced_large_model(self):
        """初始化增强版大模型组件（LLM）"""
        large_config = self.config['large_model']
        
        # LLM模型配置
        self.llm_model_name = large_config.get('llm_model', 'llama-3b')
        self.load_in_8bit = large_config.get('load_in_8bit', True)
        
        # 加载预训练LLM模型
        if self.llm_model_name == 'llama-3b':
            model_id = "meta-llama/Llama-3.2-3B-Instruct"
        else:
            model_id = self.llm_model_name
        
        # 加载模型和分词器
        if self.load_in_8bit:
            self.llm_model = AutoModelForCausalLM.from_pretrained(
                model_id,
                device_map=self.device,
                torch_dtype=torch.float16,
                load_in_8bit=True
            )
        else:
            self.llm_model = AutoModelForCausalLM.from_pretrained(
                model_id,
                device_map=self.device,
                torch_dtype=torch.float16
            )
        
        self.llm_tokenizer = AutoTokenizer.from_pretrained(model_id, use_fast=False)
        
        # 添加特殊Token（关键：保留LLM-SRec的序列理解能力）
        special_tokens = {
            'pad_token': '[PAD]',
            'additional_special_tokens': [
                '[UserRep]',      # 用户表示标记
                '[HistoryEmb]',   # 历史嵌入标记
                '[UserOut]',      # 用户输出标记
                '[ItemOut]',      # 物品输出标记
                '[SeqStart]',     # 序列开始标记
                '[SeqEnd]'        # 序列结束标记
            ]
        }
        
        self.llm_tokenizer.add_special_tokens(special_tokens)
        self.llm_model.resize_token_embeddings(len(self.llm_tokenizer))
        
        # 准备模型进行量化训练
        self.llm_model = prepare_model_for_kbit_training(self.llm_model)
        
        # 冻结大部分参数，只训练特定层
        for name, param in self.llm_model.named_parameters():
            param.requires_grad = False
        
        # 特殊Token嵌入（可训练）
        self.llm_hidden_size = self.llm_model.config.hidden_size
        
        # 用户和物品的特殊嵌入
        self.user_cls_emb = nn.Parameter(
            torch.normal(0, 1, size=(1, self.llm_hidden_size))
        )
        self.item_cls_emb = nn.Parameter(
            torch.normal(0, 1, size=(1, self.llm_hidden_size))
        )
        
        # 序列历史嵌入投影层
        self.history_projector = nn.Sequential(
            nn.Linear(self.hidden_units, self.llm_hidden_size),
            nn.LayerNorm(self.llm_hidden_size),
            nn.GELU(),
            nn.Dropout(0.1)
        )
        
        # 用户表示预测头
        self.pred_user = nn.Sequential(
            nn.Linear(self.llm_hidden_size, 2048),
            nn.LayerNorm(2048),
            nn.LeakyReLU(),
            nn.Linear(2048, 128)
        )
        
        # 物品表示预测头
        self.pred_item = nn.Sequential(
            nn.Linear(self.llm_hidden_size, 2048),
            nn.LayerNorm(2048),
            nn.LeakyReLU(),
            nn.Linear(2048, 128)
        )
        
        # CF表示对齐层
        self.pred_user_cf_align = nn.Sequential(
            nn.Linear(self.hidden_units, 256),
            nn.LayerNorm(256),
            nn.ReLU(),
            nn.Linear(256, 128)
        )
        
        logger.info(f"Enhanced LLM model initialized with special tokens")
    
    def _init_enhanced_collaboration(self):
        """初始化增强协同机制"""
        collab_config = self.config['collaboration']
        
        # 知识蒸馏参数
        self.distillation_temperature = collab_config.get('distillation_temperature', 4.0)
        self.uniformity_weight = collab_config.get('uniformity_weight', 0.1)
        
        # 序列位置感知注意力
        self.position_aware_attention = nn.MultiheadAttention(
            embed_dim=self.llm_hidden_size,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        # 序列-文本融合层
        self.seq_text_fusion = nn.Sequential(
            nn.Linear(self.hidden_units + self.llm_hidden_size, self.llm_hidden_size),
            nn.LayerNorm(self.llm_hidden_size),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(self.llm_hidden_size, self.llm_hidden_size)
        )
        
        logger.info("Enhanced collaboration components initialized")
    
    def _init_loss_functions(self):
        """初始化损失函数"""
        # 推荐损失
        self.rec_loss_fn = nn.CrossEntropyLoss(ignore_index=0)
        
        # 知识蒸馏损失
        self.kl_loss = nn.KLDivLoss(reduction='batchmean')
        
        # 对齐损失
        self.mse_loss = nn.MSELoss()
        
        logger.info("Loss functions initialized")
    
    def uniformity_loss(self, embeddings: torch.Tensor, t: float = 2.0) -> torch.Tensor:
        """
        均匀性损失：防止表示坍塌
        
        这是LLM-SRec的关键组件，确保学习到的表示在空间中均匀分布
        """
        embeddings = F.normalize(embeddings, dim=-1)
        return torch.pdist(embeddings, p=2).pow(2).mul(-t).exp().mean().log()
    
    def replace_special_tokens(self, llm_tokens: Dict[str, torch.Tensor], 
                             inputs_embeds: torch.Tensor,
                             special_embeddings: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        替换特殊Token为对应的嵌入
        
        这是LLM-SRec提高序列理解能力的核心机制
        """
        batch_size = inputs_embeds.size(0)
        
        for token_name, token_emb in special_embeddings.items():
            # 获取特殊token的位置
            token_id = self.llm_tokenizer.convert_tokens_to_ids(token_name)
            
            for batch_idx in range(batch_size):
                token_positions = (llm_tokens['input_ids'][batch_idx] == token_id).nonzero(as_tuple=True)[0]
                
                for pos in token_positions:
                    if token_emb.dim() == 2:
                        inputs_embeds[batch_idx, pos] = token_emb.squeeze(0)
                    else:
                        inputs_embeds[batch_idx, pos] = token_emb[batch_idx]
        
        return inputs_embeds
    
    def generate_text_prompt(self, user_seq: torch.Tensor, item_descriptions: List[str] = None) -> List[str]:
        """
        生成文本提示
        
        将序列推荐任务转换为文本生成任务
        """
        batch_size = user_seq.size(0)
        prompts = []
        
        for i in range(batch_size):
            # 获取用户历史序列（去除padding）
            seq = user_seq[i]
            valid_items = seq[seq != 0].tolist()
            
            if item_descriptions and len(item_descriptions) > max(valid_items):
                # 使用物品描述
                history_text = ", ".join([f"Item {item}" for item in valid_items[-10:]])  # 取最近10个
            else:
                # 使用物品ID
                history_text = ", ".join([f"Item {item}" for item in valid_items[-10:]])
            
            prompt = f"Based on the user's purchase history: [SeqStart] {history_text} [SeqEnd] [HistoryEmb], predict the next item the user will purchase. [UserOut]"
            prompts.append(prompt)
        
        return prompts
    
    def small_model_forward(self, input_ids: torch.Tensor, attention_mask: torch.Tensor = None) -> Dict[str, torch.Tensor]:
        """小模型前向传播（CF-SRec）"""
        batch_size, seq_len = input_ids.shape
        
        # 位置编码
        positions = torch.arange(seq_len, device=input_ids.device).unsqueeze(0).expand(batch_size, -1)
        
        # 嵌入层
        item_embeddings = self.item_emb(input_ids)
        pos_embeddings = self.pos_emb(positions)
        
        # 输入表示
        hidden_states = item_embeddings + pos_embeddings
        hidden_states = F.dropout(hidden_states, p=self.dropout_rate, training=self.training)
        
        # 注意力掩码处理
        if attention_mask is None:
            attention_mask = (input_ids != 0).float()
        
        # 因果掩码
        causal_mask = torch.tril(torch.ones(seq_len, seq_len, device=input_ids.device))
        causal_mask = causal_mask.unsqueeze(0).expand(batch_size, -1, -1)
        
        # 多层Transformer
        for i in range(self.num_blocks):
            # 自注意力
            residual = hidden_states
            hidden_states = self.layer_norms[i * 2](hidden_states)
            
            attn_output, attn_weights = self.attention_layers[i](
                hidden_states, hidden_states, hidden_states,
                attn_mask=causal_mask,
                key_padding_mask=(attention_mask == 0)
            )
            hidden_states = residual + attn_output
            
            # 前馈网络
            residual = hidden_states
            hidden_states = self.layer_norms[i * 2 + 1](hidden_states)
            ff_output = self.feed_forward_layers[i](hidden_states)
            hidden_states = residual + ff_output
        
        # 最终层归一化
        hidden_states = self.last_layernorm(hidden_states)
        
        # 用户表示（取最后一个有效位置）
        last_positions = attention_mask.sum(dim=1) - 1
        user_representation = hidden_states[torch.arange(batch_size), last_positions]
        
        return {
            'hidden_states': hidden_states,
            'user_representation': user_representation,
            'attention_weights': attn_weights,
            'attention_mask': attention_mask
        }
    
    def enhanced_large_model_forward(self, user_seq: torch.Tensor, 
                                   small_outputs: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """增强版大模型前向传播"""
        batch_size = user_seq.size(0)
        
        # 生成文本提示
        text_prompts = self.generate_text_prompt(user_seq)
        
        # 分词
        llm_tokens = self.llm_tokenizer(
            text_prompts,
            return_tensors="pt",
            padding="longest",
            truncation=True,
            max_length=1024
        ).to(self.device)
        
        # 获取输入嵌入
        inputs_embeds = self.llm_model.get_input_embeddings()(llm_tokens['input_ids'])
        
        # 准备特殊嵌入
        history_emb = self.history_projector(small_outputs['user_representation'])
        
        special_embeddings = {
            '[HistoryEmb]': history_emb,
            '[UserOut]': self.user_cls_emb.expand(batch_size, -1)
        }
        
        # 替换特殊Token
        inputs_embeds = self.replace_special_tokens(llm_tokens, inputs_embeds, special_embeddings)
        
        # LLM前向传播
        with torch.amp.autocast('cuda'):
            outputs = self.llm_model.forward(
                inputs_embeds=inputs_embeds,
                output_hidden_states=True
            )
        
        # 提取用户表示
        user_out_positions = self._get_token_positions(llm_tokens, '[UserOut]')
        user_outputs = self._extract_token_representations(outputs.hidden_states[-1], user_out_positions)
        
        # 预测头
        user_pred = self.pred_user(user_outputs)
        
        return {
            'user_outputs': user_outputs,
            'user_pred': user_pred,
            'llm_hidden_states': outputs.hidden_states[-1]
        }
    
    def _get_token_positions(self, llm_tokens: Dict[str, torch.Tensor], token: str) -> List[torch.Tensor]:
        """获取特殊token的位置"""
        token_id = self.llm_tokenizer.convert_tokens_to_ids(token)
        positions = []
        
        for i in range(llm_tokens['input_ids'].size(0)):
            pos = (llm_tokens['input_ids'][i] == token_id).nonzero(as_tuple=True)[0]
            positions.append(pos)
        
        return positions
    
    def _extract_token_representations(self, hidden_states: torch.Tensor, 
                                     positions: List[torch.Tensor]) -> torch.Tensor:
        """提取特殊token位置的表示"""
        representations = []
        
        for i, pos in enumerate(positions):
            if len(pos) > 0:
                # 如果有多个位置，取平均
                repr = hidden_states[i, pos].mean(dim=0)
            else:
                # 如果没有找到，使用零向量
                repr = torch.zeros(hidden_states.size(-1), device=hidden_states.device)
            
            representations.append(repr.unsqueeze(0))
        
        return torch.cat(representations, dim=0)
    
    def forward(self, input_ids: torch.Tensor, attention_mask: torch.Tensor = None,
                targets: torch.Tensor = None) -> Dict[str, torch.Tensor]:
        """完整前向传播"""
        # 小模型前向传播
        small_outputs = self.small_model_forward(input_ids, attention_mask)
        
        # 大模型前向传播
        large_outputs = self.enhanced_large_model_forward(input_ids, small_outputs)
        
        # CF表示对齐
        cf_aligned = self.pred_user_cf_align(small_outputs['user_representation'])
        
        # 计算损失
        outputs = {
            **small_outputs,
            **large_outputs,
            'cf_aligned': cf_aligned
        }
        
        if targets is not None:
            losses = self.compute_enhanced_losses(outputs, targets)
            outputs.update(losses)
        
        return outputs
    
    def compute_enhanced_losses(self, outputs: Dict[str, torch.Tensor], 
                              targets: torch.Tensor) -> Dict[str, torch.Tensor]:
        """计算增强损失"""
        # 推荐损失
        rec_loss = F.cross_entropy(outputs['user_pred'], targets)
        
        # 知识蒸馏损失（LLM向CF学习）
        llm_repr = F.normalize(outputs['user_pred'], p=2, dim=1)
        cf_repr = F.normalize(outputs['cf_aligned'], p=2, dim=1)
        
        match_loss = self.mse_loss(llm_repr, cf_repr)
        
        # 均匀性损失（防止表示坍塌）
        uniformity_loss = (
            self.uniformity_loss(llm_repr) + 
            self.uniformity_loss(cf_repr)
        ) * self.uniformity_weight
        
        # 总损失
        total_loss = rec_loss + match_loss + uniformity_loss
        
        return {
            'total_loss': total_loss,
            'rec_loss': rec_loss,
            'match_loss': match_loss,
            'uniformity_loss': uniformity_loss
        }
